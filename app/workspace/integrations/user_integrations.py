from typing import Any, cast
from uuid import UUID

from app.common.helpers.logger import get_logger
from app.core.database import Session
from app.integrations.factory import IntegrationFactory, create_factory
from app.integrations.protocols import CRMResource, MessagingResource
from app.workspace.integrations.credentials_resolver import UserCredentialsResolver
from app.workspace.models import IntegrationConfig, OAuthToken
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.services.salesforce_connection import SalesforceConnectionService
from app.workspace.types import IntegrationType

IntegrationProvider = CRMResource | MessagingResource

logger = get_logger()


class UserIntegrations:
    """
    Manages integration access for a specific user in an organization.

    Provides a clean interface to various integration providers (CRM, Messaging, etc.) by
    automatically selecting the appropriate configuration and credentials based on
    the user and organization context.
    """

    def __init__(
        self,
        user_id: UUID,
        environment: OrgEnvironment,
        integration_cfg_service: IntegrationConfigService,
        salesforce_connection_service: SalesforceConnectionService,
        db_session: Session,
    ):
        self.integration_cfg_service = integration_cfg_service
        self.salesforce_connection_service = salesforce_connection_service
        self.db_session = db_session
        self.user_id = user_id
        self.environment = environment
        self.org_id = environment.organization_id
        self.env_type = environment.type

        self._factory = self._create_factory()

        self._resources: dict[IntegrationType, Any] = {}
        self._configs: dict[IntegrationType, IntegrationConfig | None] = {}
        self._tokens: dict[IntegrationType, OAuthToken | None] = {}

    def crm(self) -> CRMResource | None:
        resource = self._get_resource(IntegrationType.CRM)
        return cast("CRMResource", resource) if resource else None

    def get_crm_accounts(self) -> list[dict[str, Any]]:
        crm = self.crm()
        if not crm:
            return []

        crm_user_id = self.crm_user_id
        if not crm_user_id:
            return []

        return crm.list_account_access(crm_user_id)

    def sync_crm_accounts(self) -> None:
        crm = self.crm()
        crm_user_id = self.crm_user_id
        if crm and crm_user_id:
            crm.bulk_sync_account_access(crm_user_ids=[crm_user_id])

    @property
    def crm_user_id(self) -> str | None:
        token = self._get_token(IntegrationType.CRM)
        return token.external_user_id if token else None

    @property
    def crm_org_id(self) -> str | None:
        token = self._get_token(IntegrationType.CRM)
        return token.external_org_id if token else None

    def _create_factory(self) -> IntegrationFactory:
        credentials_resolver = UserCredentialsResolver(
            environment=self.environment,
            user_id=self.user_id,
            integration_config_service=self.integration_cfg_service,
            salesforce_connection_service=self.salesforce_connection_service,
        )

        return create_factory(
            tenant_id=self.environment.id,
            db_factory=lambda: self.db_session,
            credentials_resolver=credentials_resolver,
        )

    def _get_resource(
        self, integration_type: IntegrationType
    ) -> IntegrationProvider | None:
        if integration_type in self._resources:
            return self._resources[integration_type]

        try:
            config = self._get_config(integration_type)
            if not config:
                logger.warning(
                    f"No active {integration_type.value} integration found for organization {self.org_id}"
                )
                return None

            resource: Any
            if integration_type == IntegrationType.CRM:
                token = self._get_token(IntegrationType.CRM)
                if not token:
                    logger.warning(
                        f"No CRM OAuth token found for user {self.user_id}, integration {config.id}"
                    )
                    return None

                resource = self._factory.crm(config.source)
            else:
                raise ValueError(
                    f"Provider type {integration_type} not yet implemented"
                )

            if resource is not None:
                self._resources[integration_type] = resource

            return resource

        except Exception:
            logger.exception(f"Error initializing {integration_type.value} provider")
            return None

    def _get_config(
        self, integration_type: IntegrationType
    ) -> IntegrationConfig | None:
        if integration_type in self._configs:
            return self._configs[integration_type]

        if integration_type == IntegrationType.CRM:
            config = self.integration_cfg_service.get_crm_config(
                environment=self.environment
            )
        else:
            logger.warning(f"Config type {integration_type} not yet implemented")
            config = None

        if config is not None:
            self._configs[integration_type] = config

        return config

    def _get_token(self, integration_type: IntegrationType) -> OAuthToken | None:
        if integration_type in self._tokens:
            return self._tokens[integration_type]

        config = self._get_config(integration_type)
        if not config:
            return None

        token = self.integration_cfg_service.get_oauth_token_for_user(
            integration_config_id=config.id, user_id=self.user_id
        )

        if token is not None:
            self._tokens[integration_type] = token

        return token
