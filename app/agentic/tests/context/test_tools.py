import asyncio

import pytest
from pydantic import BaseModel, Field

from app.agentic.context.schemas import (
    CreateContact,
    CreateEvent,
    CreateTask,
    GetAccount,
    GetContact,
    GetCurrentUserTerritory,
    GetEvent,
    GetOpportunity,
    GetTask,
    ListContactsByAccount,
    ListEventsByAccount,
    ListEventsByContact,
    ListOpportunitiesByAccount,
    ListTasksByAccount,
    ListTasksByContact,
    SearchAccounts,
    SearchContacts,
    SearchOpportunities,
    ToolDefinition,
    UpdateAccount,
    UpdateContact,
    UpdateEvent,
    UpdateOpportunity,
    UpdateTask,
)
from app.agentic.context.tools import LinkupSearchInput, get_tools


def test_get_tools(
    mocker,
    user_id,
    mock_user_integrations_instance,
    mock_crm_provider,
):
    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"
    mock_linkup_tool.args_schema = LinkupSearchInput

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    mock_crm_provider.get_opportunity = mocker.MagicMock()
    mock_crm_provider.update_opportunity = mocker.MagicMock()
    mock_crm_provider.list_opportunities_by_account = mocker.MagicMock()
    mock_crm_provider.search_opportunities = mocker.MagicMock()
    mock_crm_provider.get_account = mocker.MagicMock()
    mock_crm_provider.update_account = mocker.MagicMock()
    mock_crm_provider.search_accounts = mocker.MagicMock()
    mock_crm_provider.get_contact = mocker.MagicMock()
    mock_crm_provider.create_contact = mocker.MagicMock()
    mock_crm_provider.update_contact = mocker.MagicMock()
    mock_crm_provider.list_contacts_by_account = mocker.MagicMock()
    mock_crm_provider.search_contacts = mocker.MagicMock()
    mock_crm_provider.get_task = mocker.MagicMock()
    mock_crm_provider.create_task = mocker.MagicMock()
    mock_crm_provider.update_task = mocker.MagicMock()
    mock_crm_provider.list_tasks_by_contact = mocker.MagicMock()
    mock_crm_provider.list_tasks_by_account = mocker.MagicMock()
    mock_crm_provider.get_event = mocker.MagicMock()
    mock_crm_provider.create_event = mocker.MagicMock()
    mock_crm_provider.update_event = mocker.MagicMock()
    mock_crm_provider.list_events_by_contact = mocker.MagicMock()
    mock_crm_provider.list_events_by_account = mocker.MagicMock()

    tools = get_tools(user_id, mock_user_integrations_instance)

    mock_user_integrations_instance.crm.assert_called_once()

    assert len(tools) == 24

    tool_details = {
        "get_opportunity": (
            "Fetch a CRM opportunity by its ID",
            GetOpportunity,
            mock_crm_provider.get_opportunity,
        ),
        "update_opportunity": (
            "Update a CRM opportunity with provided fields",
            UpdateOpportunity,
            mock_crm_provider.update_opportunity,
        ),
        "list_opportunities_by_account": (
            "List CRM opportunities for a given account",
            ListOpportunitiesByAccount,
            mock_crm_provider.list_opportunities_by_account,
        ),
        "search_opportunities": (
            "Search CRM opportunities by criteria",
            SearchOpportunities,
            mock_crm_provider.search_opportunities,
        ),
        "get_account": (
            "Fetch a CRM account by its ID",
            GetAccount,
            mock_crm_provider.get_account,
        ),
        "update_account": (
            "Update a CRM account with provided fields",
            UpdateAccount,
            mock_crm_provider.update_account,
        ),
        "search_accounts": (
            "Search CRM accounts by criteria",
            SearchAccounts,
            mock_crm_provider.search_accounts,
        ),
        "get_contact": (
            "Fetch a CRM contact by its ID",
            GetContact,
            mock_crm_provider.get_contact,
        ),
        "create_contact": (
            "Create a new CRM contact",
            CreateContact,
            mock_crm_provider.create_contact,
        ),
        "update_contact": (
            "Update a CRM contact with provided fields",
            UpdateContact,
            mock_crm_provider.update_contact,
        ),
        "list_contacts_by_account": (
            "List CRM contacts for a given account",
            ListContactsByAccount,
            mock_crm_provider.list_contacts_by_account,
        ),
        "search_contacts": (
            "Search CRM contacts by criteria",
            SearchContacts,
            mock_crm_provider.search_contacts,
        ),
        "get_task": (
            "Fetch a CRM task by its ID",
            GetTask,
            mock_crm_provider.get_task,
        ),
        "create_task": (
            "Create a new CRM task",
            CreateTask,
            mock_crm_provider.create_task,
        ),
        "update_task": (
            "Update a CRM task with provided fields",
            UpdateTask,
            mock_crm_provider.update_task,
        ),
        "list_tasks_by_contact": (
            "List CRM tasks for a given contact",
            ListTasksByContact,
            mock_crm_provider.list_tasks_by_contact,
        ),
        "list_tasks_by_account": (
            "List CRM tasks for a given account",
            ListTasksByAccount,
            mock_crm_provider.list_tasks_by_account,
        ),
        "get_event": (
            "Fetch a CRM event by its ID",
            GetEvent,
            mock_crm_provider.get_event,
        ),
        "create_event": (
            "Create a new CRM event",
            CreateEvent,
            mock_crm_provider.create_event,
        ),
        "update_event": (
            "Update a CRM event with provided fields",
            UpdateEvent,
            mock_crm_provider.update_event,
        ),
        "list_events_by_contact": (
            "List CRM events for a given contact",
            ListEventsByContact,
            mock_crm_provider.list_events_by_contact,
        ),
        "list_events_by_account": (
            "List CRM events for a given account",
            ListEventsByAccount,
            mock_crm_provider.list_events_by_account,
        ),
    }

    expected_names = list(tool_details.keys())
    actual_names = [tool.name for tool in tools]

    for expected_name in expected_names:
        assert expected_name in actual_names

    assert "search_web" in actual_names
    assert "get_current_user_territory" in actual_names

    crm_tools = [tool for tool in tools if tool.name in tool_details]
    for tool in crm_tools:
        assert isinstance(tool, ToolDefinition)
        assert tool.name in tool_details
        expected_description, expected_schema, _ = tool_details[tool.name]
        assert tool.description == expected_description
        assert tool.args_schema == expected_schema
        assert tool.coroutine is not None
        assert asyncio.iscoroutinefunction(tool.coroutine)

    search_web_tool = next(tool for tool in tools if tool.name == "search_web")
    assert isinstance(search_web_tool, ToolDefinition)
    assert search_web_tool.name == "search_web"
    assert search_web_tool.description == "Search the web for information"
    assert search_web_tool.args_schema == LinkupSearchInput
    assert search_web_tool.coroutine is not None
    assert asyncio.iscoroutinefunction(search_web_tool.coroutine)

    get_current_user_territory_tool = next(
        tool for tool in tools if tool.name == "get_current_user_territory"
    )
    assert isinstance(get_current_user_territory_tool, ToolDefinition)
    assert get_current_user_territory_tool.name == "get_current_user_territory"
    assert (
        get_current_user_territory_tool.description
        == "Get my sales territory - shows which accounts I have access to manage"
    )
    assert get_current_user_territory_tool.args_schema == GetCurrentUserTerritory
    assert get_current_user_territory_tool.coroutine is not None
    assert asyncio.iscoroutinefunction(get_current_user_territory_tool.coroutine)


@pytest.mark.anyio
async def test_tool_functions(
    mocker,
    user_id,
    mock_crm_provider,
    mock_user_integrations_instance,
):
    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"

    class SearchWebSchema(BaseModel):
        query: str = Field(description="The search query")

    mock_linkup_tool.args_schema = SearchWebSchema

    mock_result = mocker.MagicMock()

    result_1 = mocker.MagicMock()
    result_1.name = "Test Result 1"
    result_1.url = "https://example.com/1"
    result_1.content = "This is test content 1"

    result_2 = mocker.MagicMock()
    result_2.name = "Test Result 2"
    result_2.url = "https://example.com/2"
    result_2.content = "This is test content 2"

    mock_result.results = [result_1, result_2]
    mock_linkup_tool.invoke.return_value = mock_result

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    tools = get_tools(user_id, mock_user_integrations_instance)

    tool_map = {t.name: t for t in tools}

    get_opportunity_tool = tool_map["get_opportunity"]
    update_opportunity_tool = tool_map["update_opportunity"]
    list_opportunities_by_account_tool = tool_map["list_opportunities_by_account"]
    search_opportunities_tool = tool_map["search_opportunities"]
    get_account_tool = tool_map["get_account"]
    update_account_tool = tool_map["update_account"]
    search_accounts_tool = tool_map["search_accounts"]
    get_contact_tool = tool_map["get_contact"]
    create_contact_tool = tool_map["create_contact"]
    update_contact_tool = tool_map["update_contact"]
    list_contacts_by_account_tool = tool_map["list_contacts_by_account"]
    search_contacts_tool = tool_map["search_contacts"]
    get_task_tool = tool_map["get_task"]
    create_task_tool = tool_map["create_task"]
    update_task_tool = tool_map["update_task"]
    list_tasks_by_contact_tool = tool_map["list_tasks_by_contact"]
    list_tasks_by_account_tool = tool_map["list_tasks_by_account"]
    get_event_tool = tool_map["get_event"]
    create_event_tool = tool_map["create_event"]
    update_event_tool = tool_map["update_event"]
    list_events_by_contact_tool = tool_map["list_events_by_contact"]
    list_events_by_account_tool = tool_map["list_events_by_account"]
    search_web_tool = tool_map["search_web"]

    mock_crm_provider.get_opportunity.reset_mock()
    mock_crm_provider.update_opportunity.reset_mock()
    mock_crm_provider.list_opportunities_by_account.reset_mock()
    mock_crm_provider.search_opportunities.reset_mock()
    mock_crm_provider.get_account.reset_mock()
    mock_crm_provider.update_account.reset_mock()
    mock_crm_provider.search_accounts.reset_mock()
    mock_crm_provider.get_contact.reset_mock()
    mock_crm_provider.create_contact.reset_mock()
    mock_crm_provider.update_contact.reset_mock()
    mock_crm_provider.list_contacts_by_account.reset_mock()
    mock_crm_provider.search_contacts.reset_mock()
    mock_crm_provider.get_task.reset_mock()
    mock_crm_provider.create_task.reset_mock()
    mock_crm_provider.update_task.reset_mock()
    mock_crm_provider.list_tasks_by_contact.reset_mock()
    mock_crm_provider.list_tasks_by_account.reset_mock()
    mock_crm_provider.get_event.reset_mock()
    mock_crm_provider.create_event.reset_mock()
    mock_crm_provider.update_event.reset_mock()
    mock_crm_provider.list_events_by_contact.reset_mock()
    mock_crm_provider.list_events_by_account.reset_mock()

    await get_opportunity_tool.coroutine("001")
    mock_crm_provider.get_opportunity.assert_called_once_with("001")

    fields = {"Name": "Updated Opportunity"}
    await update_opportunity_tool.coroutine("001", fields)
    mock_crm_provider.update_opportunity.assert_called_once_with("001", fields)

    await list_opportunities_by_account_tool.coroutine("account123")
    mock_crm_provider.list_opportunities_by_account.assert_called_once_with(
        "account123"
    )

    search_opp_criteria = {"Name": "Big Deal"}
    await search_opportunities_tool.coroutine(search_opp_criteria)
    mock_crm_provider.search_opportunities.assert_called_once_with(search_opp_criteria)

    await get_account_tool.coroutine("002")
    mock_crm_provider.get_account.assert_called_once_with("002")

    account_fields = {"Name": "Updated Account"}
    await update_account_tool.coroutine("002", account_fields)
    mock_crm_provider.update_account.assert_called_once_with("002", account_fields)

    search_acc_criteria = {"Name": "Tech Corp"}
    await search_accounts_tool.coroutine(search_acc_criteria)
    mock_crm_provider.search_accounts.assert_called_once_with(search_acc_criteria)

    await get_contact_tool.coroutine("003")
    mock_crm_provider.get_contact.assert_called_once_with("003")

    contact_data = {"FirstName": "John", "LastName": "Doe"}
    await create_contact_tool.coroutine(contact_data)
    mock_crm_provider.create_contact.assert_called_once_with(contact_data)

    contact_update_data = {"FirstName": "Jane"}
    await update_contact_tool.coroutine("003", contact_update_data)
    mock_crm_provider.update_contact.assert_called_once_with("003", contact_update_data)

    await list_contacts_by_account_tool.coroutine("002")
    mock_crm_provider.list_contacts_by_account.assert_called_once_with("002")

    search_criteria = {"Email": "<EMAIL>"}
    await search_contacts_tool.coroutine(search_criteria)
    mock_crm_provider.search_contacts.assert_called_once_with(search_criteria)

    await get_task_tool.coroutine("00T")
    mock_crm_provider.get_task.assert_called_once_with("00T")

    task_data = {"Subject": "Call client", "Status": "Not Started"}
    await create_task_tool.coroutine(task_data)
    mock_crm_provider.create_task.assert_called_once_with(task_data)

    task_update_data = {"Status": "Completed"}
    await update_task_tool.coroutine("00T", task_update_data)
    mock_crm_provider.update_task.assert_called_once_with("00T", task_update_data)

    await list_tasks_by_contact_tool.coroutine("003")
    mock_crm_provider.list_tasks_by_contact.assert_called_once_with("003")

    await list_tasks_by_account_tool.coroutine("001")
    mock_crm_provider.list_tasks_by_account.assert_called_once_with("001")

    await get_event_tool.coroutine("00U")
    mock_crm_provider.get_event.assert_called_once_with("00U")

    event_data = {"Subject": "Client Meeting", "StartDateTime": "2024-01-15T10:00:00Z"}
    await create_event_tool.coroutine(event_data)
    mock_crm_provider.create_event.assert_called_once_with(event_data)

    event_update_data = {"Subject": "Updated Meeting"}
    await update_event_tool.coroutine("00U", event_update_data)
    mock_crm_provider.update_event.assert_called_once_with("00U", event_update_data)

    await list_events_by_contact_tool.coroutine("003")
    mock_crm_provider.list_events_by_contact.assert_called_once_with("003")

    await list_events_by_account_tool.coroutine("001")
    mock_crm_provider.list_events_by_account.assert_called_once_with("001")

    result = await search_web_tool.coroutine("test query")
    mock_linkup_tool.invoke.assert_called_once_with({"query": "test query"})

    expected_result = (
        "Title: Test Result 1\nURL: https://example.com/1\nContent: This is test content 1\n\n\n"
        "Title: Test Result 2\nURL: https://example.com/2\nContent: This is test content 2\n"
    )
    assert result == expected_result


@pytest.mark.anyio
async def test_get_current_user_territory(
    mocker,
    user_id,
    mock_user_integrations_instance,
):
    mock_accounts = [
        {"id": "acc_1", "name": "Territory Account 1", "territory": "North"},
        {"id": "acc_2", "name": "Territory Account 2", "territory": "South"},
    ]

    mock_user_integrations_instance.get_crm_accounts.return_value = mock_accounts

    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"
    mock_linkup_tool.args_schema = mocker.MagicMock()

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    tools = get_tools(user_id, mock_user_integrations_instance)

    tool_map = {t.name: t for t in tools}
    get_current_user_territory_tool = tool_map["get_current_user_territory"]

    result = await get_current_user_territory_tool.coroutine()

    mock_user_integrations_instance.get_crm_accounts.assert_called_once()

    assert result == mock_accounts


@pytest.mark.anyio
async def test_get_current_user_territory_empty_list(
    mocker,
    user_id,
    mock_user_integrations_instance,
):
    mock_user_integrations_instance.get_crm_accounts.return_value = []

    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"
    mock_linkup_tool.args_schema = mocker.MagicMock()

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    tools = get_tools(user_id, mock_user_integrations_instance)

    tool_map = {t.name: t for t in tools}
    get_current_user_territory_tool = tool_map["get_current_user_territory"]

    result = await get_current_user_territory_tool.coroutine()

    mock_user_integrations_instance.get_crm_accounts.assert_called_once()

    assert result == []


@pytest.mark.anyio
async def test_search_web_no_results(
    mocker,
    user_id,
    mock_user_integrations_instance,
):
    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"

    class SearchWebSchema(BaseModel):
        query: str = Field(description="The search query")

    mock_linkup_tool.args_schema = SearchWebSchema

    mock_result = mocker.MagicMock()
    mock_result.results = []
    mock_linkup_tool.invoke.return_value = mock_result

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    tools = get_tools(user_id, mock_user_integrations_instance)
    tool_map = {t.name: t for t in tools}
    search_web_tool = tool_map["search_web"]

    result = await search_web_tool.coroutine("no results query")
    mock_linkup_tool.invoke.assert_called_once_with({"query": "no results query"})
    assert result == "No search results found."


@pytest.mark.anyio
async def test_search_web_no_results_attribute(
    mocker,
    user_id,
    mock_user_integrations_instance,
):
    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"

    class SearchWebSchema(BaseModel):
        query: str = Field(description="The search query")

    mock_linkup_tool.args_schema = SearchWebSchema

    mock_result = mocker.MagicMock()
    del mock_result.results
    mock_linkup_tool.invoke.return_value = mock_result

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    tools = get_tools(user_id, mock_user_integrations_instance)
    tool_map = {t.name: t for t in tools}
    search_web_tool = tool_map["search_web"]

    result = await search_web_tool.coroutine("no results attribute query")
    mock_linkup_tool.invoke.assert_called_once_with(
        {"query": "no results attribute query"}
    )
    assert result == "No search results found."


def test_get_tools_no_integration(
    user_id,
    mock_user_integrations_instance,
) -> None:
    mock_user_integrations_instance.crm.return_value = None

    with pytest.raises(
        RuntimeError,
        match=f"No CRM integration configured for user {user_id}",
    ):
        get_tools(user_id, mock_user_integrations_instance)

    mock_user_integrations_instance.crm.assert_called_once()
