import json
import uuid
from collections.abc import As<PERSON><PERSON>tera<PERSON>
from datetime import UTC, datetime
from uuid import uuid4

import pytest
from langchain_core.messages import HumanMessage

from app.agentic.exceptions import ThreadOwnershipError
from app.agentic.graph.graph_manager import GraphManager
from app.agentic.models import OrganizationMemberThread
from app.agentic.repository import OrganizationMemberThreadRepository
from app.agentic.schemas import ChatRequest, ThreadRead
from app.agentic.service import AgentService
from app.workspace.schemas import OrgEnvironment
from app.workspace.types import EnvironmentType


@pytest.fixture
def mock_graph_manager(mocker):
    mock = mocker.AsyncMock(spec=GraphManager)
    return mock


@pytest.fixture
def mock_organization_member_thread_repository(mocker):
    mock = mocker.Mock(spec=OrganizationMemberThreadRepository)
    return mock


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        id=uuid4(),
        organization_id=uuid4(),
        type=EnvironmentType.PROD,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def agent_service(
    org_id,
    user_id,
    org_member_id,
    mock_environment,
    db_session,
    mock_graph_manager,
    mock_organization_member_thread_repository,
):
    return AgentService(
        org_id=org_id,
        user_id=user_id,
        org_member_id=org_member_id,
        environment=mock_environment,
        db_session=db_session,
        graph_manager=mock_graph_manager,
        organization_member_thread_repository=mock_organization_member_thread_repository,
    )


@pytest.fixture
def chat_request():
    return ChatRequest(
        message="Hello, how can you help me?",
        thread_id=str(uuid.uuid4()),
        crm_account_id="test-crm-account-id",
    )


@pytest.mark.anyio
async def test_process_message_stream_with_new_thread(
    agent_service,
    chat_request,
    mock_graph_manager,
    mock_organization_member_thread_repository,
    org_id,
    user_id,
    org_member_id,
):
    expected_thread_id = chat_request.thread_id

    mock_organization_member_thread_repository.get_by_thread_id.return_value = None

    async def mock_stream_iterator() -> AsyncIterator[str]:
        metadata_data = {
            "thread_id": expected_thread_id,
            "crm_account_id": "test-crm-account-id",
        }
        yield f"event: metadata\ndata: {json.dumps(metadata_data)}\n\n"
        yield f"event: message\ndata: {json.dumps({'content': 'Streaming '})}\n\n"
        yield f"event: message\ndata: {json.dumps({'content': 'response...'})}\n\n"

    mock_graph_manager.stream_graph.return_value = mock_stream_iterator()

    stream = await agent_service.process_message_stream(chat_request)
    chunks = [chunk async for chunk in stream]

    assert len(chunks) == 3
    assert "event: metadata" in chunks[0]
    assert "event: message" in chunks[1]
    assert "event: message" in chunks[2]

    mock_graph_manager.stream_graph.assert_called_once()
    call_args, _ = mock_graph_manager.stream_graph.call_args

    graph_input = call_args[0]
    assert "messages" in graph_input
    assert isinstance(graph_input["messages"][0], HumanMessage)
    assert graph_input["messages"][0].content == "Hello, how can you help me?"
    assert graph_input["crm_account_id"] == "test-crm-account-id"
    assert graph_input["org_id"] == org_id
    assert graph_input["user_id"] == user_id
    assert graph_input["resume"] is None

    thread_id_arg = call_args[0]["thread_id"]
    assert thread_id_arg == expected_thread_id
    assert isinstance(uuid.UUID(thread_id_arg), uuid.UUID)

    mock_organization_member_thread_repository.create.assert_called_once()
    create_args = mock_organization_member_thread_repository.create.call_args
    assert create_args[1]["thread_id"] == expected_thread_id
    assert create_args[1]["organization_member_id"] == org_member_id


@pytest.mark.anyio
async def test_process_message_stream_with_existing_thread(
    agent_service,
    mock_graph_manager,
    mock_organization_member_thread_repository,
    org_id,
    user_id,
):
    existing_thread = "789e4567-e89b-12d3-a456-************"
    chat_request = ChatRequest(
        message="Another question",
        thread_id=existing_thread,
        crm_account_id="test-crm-account-id",
    )

    mock_thread = type("MockThread", (), {"thread_id": existing_thread})()
    mock_organization_member_thread_repository.get_by_thread_id.return_value = (
        mock_thread
    )

    async def mock_stream_iterator() -> AsyncIterator[str]:
        metadata_data = {
            "thread_id": existing_thread,
            "crm_account_id": "test-crm-account-id",
        }
        yield f"event: metadata\ndata: {json.dumps(metadata_data)}\n\n"
        yield f"event: message\ndata: {json.dumps({'content': 'Streaming '})}\n\n"
        yield f"event: message\ndata: {json.dumps({'content': 'response...'})}\n\n"

    mock_graph_manager.stream_graph.return_value = mock_stream_iterator()

    stream = await agent_service.process_message_stream(chat_request)
    chunks = [chunk async for chunk in stream]

    assert len(chunks) == 3
    assert "event: metadata" in chunks[0]
    assert f'"thread_id": "{existing_thread}"' in chunks[0]
    assert "event: message" in chunks[1]
    assert '"content": "Streaming "' in chunks[1]
    assert "event: message" in chunks[2]
    assert '"content": "response..."' in chunks[2]

    mock_graph_manager.stream_graph.assert_called_once()
    call_args, _ = mock_graph_manager.stream_graph.call_args

    graph_input = call_args[0]
    assert "messages" in graph_input
    assert isinstance(graph_input["messages"][0], HumanMessage)
    assert graph_input["messages"][0].content == "Another question"
    assert graph_input["crm_account_id"] == "test-crm-account-id"
    assert graph_input["org_id"] == org_id
    assert graph_input["user_id"] == user_id
    assert graph_input["resume"] is None

    thread_id_arg = call_args[0]["thread_id"]
    assert thread_id_arg == existing_thread

    mock_organization_member_thread_repository.create.assert_not_called()


@pytest.mark.anyio
async def test_process_message_stream_missing_crm_account_id(
    agent_service,
):
    chat_request = ChatRequest(
        message="Hello, how can you help me?",
        thread_id=str(uuid.uuid4()),
        crm_account_id=None,
    )

    with pytest.raises(ValueError, match="A crm_account_id is required"):
        await agent_service.process_message_stream(chat_request)


@pytest.mark.anyio
async def test_process_message_stream_missing_thread_id(
    agent_service,
):
    class MockChatRequest:
        def __init__(self):
            self.message = "Hello, how can you help me?"
            self.thread_id = None
            self.crm_account_id = "test-crm-account-id"
            self.resume = None

    mock_request = MockChatRequest()

    with pytest.raises(ValueError, match="A thread_id is required"):
        await agent_service.process_message_stream(mock_request)


@pytest.mark.anyio
async def test_process_message_stream_empty_thread_id(
    agent_service,
):
    class MockChatRequest:
        def __init__(self):
            self.message = "Hello, how can you help me?"
            self.thread_id = ""
            self.crm_account_id = "test-crm-account-id"
            self.resume = None

    mock_request = MockChatRequest()

    with pytest.raises(ValueError, match="A thread_id is required"):
        await agent_service.process_message_stream(mock_request)


@pytest.mark.anyio
async def test_process_message_stream_with_resume(
    agent_service,
    mock_graph_manager,
):
    existing_thread = "789e4567-e89b-12d3-a456-************"
    resume_token = "resume-token-123"
    chat_request = ChatRequest(
        message="Another question",
        thread_id=existing_thread,
        crm_account_id="test-crm-account-id",
        resume=resume_token,
    )

    async def mock_stream_iterator() -> AsyncIterator[str]:
        yield 'event: resume\ndata: {"content": "Resuming..."}\n\n'

    mock_graph_manager.stream_graph.return_value = mock_stream_iterator()

    stream = await agent_service.process_message_stream(chat_request)
    chunks = [chunk async for chunk in stream]

    assert len(chunks) == 1
    assert "event: resume" in chunks[0]

    mock_graph_manager.stream_graph.assert_called_once()
    call_args, _ = mock_graph_manager.stream_graph.call_args

    graph_input = call_args[0]
    assert graph_input["resume"] == resume_token


def test_get_threads_by_org_member_and_crm_account(
    agent_service,
    mock_organization_member_thread_repository,
    org_member_id,
):
    crm_account_id = "test-crm-account-id"
    thread1 = OrganizationMemberThread(
        id=uuid.uuid4(),
        thread_id="thread-1",
        organization_member_id=org_member_id,
        crm_account_id=crm_account_id,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )
    thread2 = OrganizationMemberThread(
        id=uuid.uuid4(),
        thread_id="thread-2",
        organization_member_id=org_member_id,
        crm_account_id=crm_account_id,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )
    threads = [thread1, thread2]

    mock_organization_member_thread_repository.get_by_org_member_and_crm_account.return_value = threads

    db_threads = agent_service.get_threads_by_org_member_and_crm_account(crm_account_id)

    assert len(db_threads.threads) == 2
    assert db_threads.threads[0].id == "thread-1"
    assert db_threads.threads[1].id == "thread-2"
    mock_organization_member_thread_repository.get_by_org_member_and_crm_account.assert_called_once_with(
        org_member_id, crm_account_id
    )


@pytest.mark.anyio
async def test_get_thread_history_success(
    agent_service, mock_graph_manager, mock_organization_member_thread_repository
):
    thread_id = "test-thread-id-success"
    page = 1
    size = 10

    mock_thread = ThreadRead(
        id=thread_id,
        organization_member_id=agent_service.org_member_id,
        crm_account_id="test-crm-account-id",
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )
    mock_organization_member_thread_repository.get_by_thread_id.return_value = (
        mock_thread
    )

    mock_response = {
        "pagination": {
            "thread_id": thread_id,
            "current_page": page,
            "page_size": size,
            "total_messages": 15,
            "total_pages": 2,
        },
        "messages": [
            {"role": "user", "content": [{"type": "text", "text": "Message 1"}]},
            {"role": "assistant", "content": [{"type": "text", "text": "Message 2"}]},
        ],
    }

    mock_graph_manager.get_historical_messages.return_value = mock_response

    result = await agent_service.get_thread_history(thread_id, page, size)

    assert result is not None
    assert result["pagination"]["thread_id"] == thread_id
    assert result["pagination"]["current_page"] == page
    assert result["pagination"]["total_messages"] == 15
    assert len(result["messages"]) == 2
    assert result["messages"][0]["role"] == "user"
    assert len(result["messages"][0]["content"]) == 1
    assert result["messages"][0]["content"][0]["type"] == "text"
    assert result["messages"][0]["content"][0]["text"] == "Message 1"
    assert result["messages"][1]["role"] == "assistant"
    assert len(result["messages"][1]["content"]) == 1
    assert result["messages"][1]["content"][0]["type"] == "text"
    assert result["messages"][1]["content"][0]["text"] == "Message 2"

    mock_organization_member_thread_repository.get_by_thread_id.assert_called_once_with(
        thread_id
    )
    mock_graph_manager.get_historical_messages.assert_called_once_with(
        thread_id, page, size
    )


@pytest.mark.anyio
async def test_get_thread_history_not_found(
    agent_service, mock_organization_member_thread_repository, mock_graph_manager
):
    thread_id = "non-existent-thread-id"
    page = 1
    size = 10

    mock_organization_member_thread_repository.get_by_thread_id.return_value = None

    with pytest.raises(
        ThreadOwnershipError,
        match="Thread not found or does not belong to the current user",
    ):
        await agent_service.get_thread_history(thread_id, page, size)

    mock_organization_member_thread_repository.get_by_thread_id.assert_called_once_with(
        thread_id
    )
    mock_graph_manager.get_historical_messages.assert_not_called()


@pytest.mark.anyio
async def test_get_thread_history_wrong_owner(
    agent_service, mock_organization_member_thread_repository, mock_graph_manager
):
    thread_id = "test-thread-id-wrong-owner"
    page = 1
    size = 10
    other_org_member_id = uuid.uuid4()

    assert other_org_member_id != agent_service.org_member_id

    mock_thread = ThreadRead(
        id=thread_id,
        organization_member_id=other_org_member_id,
        crm_account_id="test-crm-account-id",
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )
    mock_organization_member_thread_repository.get_by_thread_id.return_value = (
        mock_thread
    )

    with pytest.raises(
        ThreadOwnershipError,
        match="Thread not found or does not belong to the current user",
    ):
        await agent_service.get_thread_history(thread_id, page, size)

    mock_organization_member_thread_repository.get_by_thread_id.assert_called_once_with(
        thread_id
    )
    mock_graph_manager.get_historical_messages.assert_not_called()
