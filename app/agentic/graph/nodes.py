"""
Node functions for the agentic graph system.

This module contains utility functions that are still relevant for the multi-agent architecture.
The main graph logic has been moved to a class-based approach in graph.py.
"""

from langchain_google_genai import ChatGoogleGenerativeAI
from pydantic import SecretStr

from app.core.config import config


def get_llm() -> ChatGoogleGenerativeAI:
    """
    Returns a configured Gemini LLM instance.

    This function is used by the multi-agent system to get a consistent
    LLM configuration across all agents.
    """
    return ChatGoogleGenerativeAI(
        model="gemini-2.5-flash-preview-04-17",
        api_key=SecretStr(config.gemini_api_key),
    )


# Note: The following functions have been deprecated in favor of the new multi-agent architecture:
# - call_model: Now handled by create_react_agent in each specialized agent
# - fetch_account: Now implemented as a tool in the CRM agent
# - user_validation: Now implemented as a tool in the validation agent
#
# These functions are kept here temporarily for reference but should not be used
# in the new multi-agent system.
