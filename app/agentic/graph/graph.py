"""
Multi-agent graph implementation for the agentic system.

This module implements a class-based approach to LangGraph with multiple specialized agents:
- Supervisor Agent: Orchestrates work between other agents
- CRM Agent: Handles CRM and account-related tasks
- Sales Document Agent: Creates sales documents like closing plans and meeting briefs
- Validation Agent: Handles user validation for CRM updates

The system uses Gemini as the LLM and integrates with the existing tool system.
"""

from typing import Annotated, Any
from uuid import UUID

from langchain_core.runnables import Runnable
from langchain_core.tools import StructuredTool, tool
from langgraph.graph import START, StateGraph
from langgraph.prebuilt import InjectedState, create_react_agent
from langgraph.types import Command, interrupt

from app.agentic.context.tools import get_tools
from app.agentic.graph.nodes import get_llm
from app.agentic.graph.prompts import AgentPrompts
from app.agentic.graph.state import ConversationState
from app.workspace.integrations.user_integrations import UserIntegrations


class Graph:
    def __init__(
        self,
        user_id: UUID,
        user_integrations: UserIntegrations,
    ):
        self.user_id = user_id
        self.user_integrations = user_integrations

    def create_definition(self) -> StateGraph:
        crm_agent = self._create_crm_agent()
        sales_document_agent = self._create_sales_document_agent()
        validation_agent = self._create_validation_agent()
        supervisor_agent = self._create_supervisor_agent(
            "crm_agent", "sales_document_agent", "validation_agent"
        )

        graph = StateGraph(ConversationState)
        graph.add_node("supervisor", supervisor_agent)
        graph.add_node("crm_agent", crm_agent)
        graph.add_node("sales_document_agent", sales_document_agent)
        graph.add_node("validation_agent", validation_agent)

        graph.add_edge(START, "supervisor")
        graph.add_edge("crm_agent", "supervisor")
        graph.add_edge("sales_document_agent", "supervisor")
        graph.add_edge("validation_agent", "supervisor")

        return graph

    def _create_crm_agent(self) -> Runnable:
        # Use the base system prompt but customize for CRM tasks
        base_prompt = AgentPrompts.get_sales_assistant_system_prompt()
        crm_specialization = (
            "\n\n## CRM Agent Specialization\n"
            "You are now operating as a specialized CRM agent within a multi-agent system.\n"
            "Your specific responsibilities include:\n"
            "- Fetching and analyzing account data\n"
            "- Managing CRM records and updates\n"
            "- Providing account insights and recommendations\n"
            "- Coordinating with other agents through the supervisor\n\n"
            "When you complete your CRM tasks, report your findings to the supervisor.\n"
            "Be thorough but concise in your responses."
        )
        prompt = base_prompt + crm_specialization
        # The original `fetch_account` node is now a tool for this agent.
        fetch_account_tool = self._create_fetch_account_tool()
        crm_tools = self.get_langchain_tools()
        return create_react_agent(
            model=get_llm(),
            tools=[fetch_account_tool, *crm_tools],
            prompt=prompt,
            name="crm_agent",
        )

    def _create_sales_document_agent(self) -> Runnable:
        prompt = (
            "You are a specialized sales document assistant.\n"
            "Your tasks involve creating closing plans and meeting briefs.\n"
            "After you're done, respond to the supervisor directly.\n"
            "Respond ONLY with the results of your work."
        )
        return create_react_agent(
            model=get_llm(),
            tools=[self.create_closing_plan, self.create_meeting_brief],
            prompt=prompt,
            name="sales_document_agent",
        )

    def _create_validation_agent(self) -> Runnable:
        prompt = (
            "You are a validation assistant.\n"
            "Your role is to present CRM updates to users for approval.\n"
            "When you receive update details, ask the user for confirmation.\n"
            "Respond with either 'APPROVED' or 'REJECTED' based on user input."
        )
        validation_tool = self._create_user_validation_tool()
        return create_react_agent(
            model=get_llm(),
            tools=[validation_tool],
            prompt=prompt,
            name="validation_agent",
        )

    def _create_supervisor_agent(
        self,
        crm_agent_name: str,
        sales_document_agent_name: str,
        validation_agent_name: str,
    ) -> Runnable:
        handoff_to_crm = self.create_handoff_tool(
            crm_agent_name, "Assign a CRM or account-related task."
        )
        handoff_to_docs = self.create_handoff_tool(
            sales_document_agent_name, "Assign a sales document creation task."
        )
        handoff_to_validation = self.create_handoff_tool(
            validation_agent_name, "Assign a validation task for CRM updates."
        )

        prompt = (
            "You are a supervisor managing three agents:\n"
            f"- a '{crm_agent_name}'. Assign CRM and account tasks to this agent.\n"
            f"- a '{sales_document_agent_name}'. Assign document creation tasks to this agent.\n"
            f"- a '{validation_agent_name}'. Assign validation tasks for CRM updates to this agent.\n"
            "Assign work to one agent at a time. Do not do any work yourself.\n"
            "When CRM updates need approval, use the validation agent."
        )

        return create_react_agent(
            model=get_llm(),
            tools=[handoff_to_crm, handoff_to_docs, handoff_to_validation],
            prompt=prompt,
            name="supervisor",
        )

    def _create_fetch_account_tool(self) -> StructuredTool:
        def fetch_account_wrapper(crm_account_id: str) -> dict:
            return self._fetch_account_logic(self.user_integrations, crm_account_id)

        return StructuredTool.from_function(
            func=fetch_account_wrapper,
            name="fetch_account",
            description="Fetches account details for a given CRM account ID.",
        )

    def _create_user_validation_tool(self) -> StructuredTool:
        def user_validation_wrapper(update_details: dict[str, Any]) -> str:
            return self._user_validation_logic(update_details)

        return StructuredTool.from_function(
            func=user_validation_wrapper,
            name="request_user_validation",
            description="Request user validation for CRM updates before executing them.",
        )

    def _user_validation_logic(self, update_details: dict[str, Any]) -> str:
        """The core logic for user validation, adapted from nodes.py"""
        try:
            decision = interrupt(
                {
                    "question": "Do these updates in your CRM look correct?",
                    "update_to_be_made": update_details,
                }
            )

            if decision == "approve":
                return "APPROVED: User has approved the CRM update."
            else:
                return "REJECTED: User has rejected the proposed CRM update. Please revise your approach or ask for more information."
        except Exception as e:
            return f"ERROR: Could not get user validation: {str(e)}"

    def _fetch_account_logic(
        self, user_integrations: UserIntegrations, crm_account_id: str
    ) -> dict:
        """The core logic for fetching account data."""
        from app.agentic.context.account import get_account_details

        print("--- Fetching account details via tool ---")
        try:
            account_details = get_account_details(
                self.user_id,
                crm_account_id,
                user_integrations,
            )
            return account_details
        except Exception as e:
            print(f"Error fetching account details: {e}")
            # Return mock data as fallback
            return {
                "account_id": crm_account_id,
                "name": "Account details unavailable",
                "error": str(e),
            }

    def get_langchain_tools(self) -> list[StructuredTool]:
        raw_tools = get_tools(self.user_id, self.user_integrations)
        langchain_tools = [
            StructuredTool(
                name=td.name,
                description=td.description,
                func=None,
                coroutine=td.coroutine,
                args_schema=td.args_schema,
            )
            for td in raw_tools
        ]
        return langchain_tools

    @staticmethod
    def create_handoff_tool(agent_name: str, description: str) -> tool:
        @tool(f"transfer_to_{agent_name}", description=description)
        def handoff(
            state: Annotated[ConversationState, InjectedState],
        ) -> Command:
            return Command(
                goto=agent_name,
                update=state,
                graph=Command.PARENT,
            )

        return handoff

    @tool
    def create_closing_plan(opportunity_details: str) -> str:
        print(f"--- Creating Closing Plan for: {opportunity_details} ---")
        # In a real implementation, this would call a service or use a template
        # to generate a rich document.
        return f"Closing plan document generated for opportunity: {opportunity_details}"

    @tool
    def create_meeting_brief(meeting_subject: str, participants: list[str]) -> str:
        """
        Generates a meeting brief document for a sales meeting.
        """
        print(
            f"--- Creating Meeting Brief for: {meeting_subject} with {participants} ---"
        )
        # In a real implementation, this would fetch data about participants
        # and the meeting's context to create a comprehensive brief.
        return f"Meeting brief generated for '{meeting_subject}'."
