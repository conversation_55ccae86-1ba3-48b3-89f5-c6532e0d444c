# import functools
# from uuid import UUID

# from langchain_core.tools import StructuredTool
# from langgraph.graph import END, START, StateGraph
# from langgraph.prebuilt import ToolNode

# from app.agentic.context.tools import get_tools
# from app.agentic.graph.nodes import call_model, fetch_account, user_validation
# from app.workspace.integrations.user_integrations import UserIntegrations
# class GraphFactory:
#     def __init__(
#         self,
#         user_id: UUID,
#         user_integrations: UserIntegrations,
#     ):
#         self.user_id = user_id
#         self.user_integrations = user_integrations
#     def create_definition(self) -> StateGraph:
#         graph = StateGraph(state_schema=ConversationState)
#         langchain_tools = self.get_langchain_tools()
#         self.create_nodes(graph, langchain_tools)
#         self.create_edges(graph)
#         return graph
#     def create_nodes(
#         self, graph: StateGraph, langchain_tools: list[StructuredTool]
#     ) -> None:
#         tool_node_instance = ToolNode(langchain_tools)
#         partial_fetch_account = functools.partial(
#             fetch_account, user_integrations=self.user_integrations
#         )
#         partial_call_model = functools.partial(call_model, tools=langchain_tools)
#         graph.add_node("fetch_account", partial_fetch_account)
#         graph.add_node("call_model", partial_call_model)
#         graph.add_node("user_validation", user_validation)
#         graph.add_node("call_tools", tool_node_instance)
#     def create_edges(self, graph: StateGraph) -> None:
#         graph.add_conditional_edges(
#             START, self.should_fetch_account, ["fetch_account", "call_model"]
#         )
#         graph.add_edge("fetch_account", "call_model")
#         graph.add_conditional_edges(
#             "call_model",
#             self.should_execute_tools,
#             ["call_tools", "user_validation", "fetch_account", END],
#         )
#         graph.add_edge("call_tools", "call_model")
#     def get_langchain_tools(self) -> list[StructuredTool]:
#         raw_tools = get_tools(self.user_id, self.user_integrations)
#         langchain_tools = [
#             StructuredTool(
#                 name=td.name,
#                 description=td.description,
#                 func=None,
#                 coroutine=td.coroutine,
#                 args_schema=td.args_schema,
#             )
#             for td in raw_tools
#         ]
#         return langchain_tools
#     @staticmethod
#     def should_fetch_account(state: ConversationState) -> str:
#         return "fetch_account" if not state.get("last_refetch_at") else "call_model"
#     @staticmethod
#     def should_execute_tools(state: ConversationState) -> str:
#         last = state["messages"][-1]
#         if hasattr(last, "tool_calls") and last.tool_calls:
#             for tool_call in last.tool_calls:
#                 if tool_call["name"] == "update_opportunity":
#                     return "user_validation"
#             return "call_tools"
#         return END
import functools
from typing import Annotated
from uuid import UUID

from langchain_core.runnables import Runnable
from langchain_core.tools import StructuredTool, tool
from langgraph.graph import START, StateGraph
from langgraph.prebuilt import InjectedState, create_react_agent
from langgraph.types import Command

from app.agentic.context.tools import get_tools
from app.agentic.graph.state import ConversationState
from app.workspace.integrations.user_integrations import UserIntegrations


class Graph:
    def __init__(
        self,
        user_id: UUID,
        user_integrations: UserIntegrations,
    ):
        self.user_id = user_id
        self.user_integrations = user_integrations

    def create_definition(self) -> StateGraph:
        crm_agent = self._create_crm_agent()
        sales_document_agent = self._create_sales_document_agent()

        supervisor_agent = self._create_supervisor_agent(
            "crm_agent", "sales_document_agent"
        )

        graph = StateGraph(ConversationState)
        graph.add_node("supervisor", supervisor_agent)
        graph.add_node("crm_agent", crm_agent)
        graph.add_node("sales_document_agent", sales_document_agent)

        graph.add_edge(START, "supervisor")
        graph.add_edge("crm_agent", "supervisor")
        graph.add_edge("sales_document_agent", "supervisor")

        return graph

    def _create_crm_agent(self) -> Runnable:
        """
        Creates the agent responsible for CRM and account-related tasks.
        """
        prompt = (
            "You are a specialized CRM assistant.\n"
            "Your tasks involve fetching account data and managing CRM records.\n"
            "After you're done, respond to the supervisor directly.\n"
            "Respond ONLY with the results of your work."
        )
        # The original `fetch_account` node is now a tool for this agent.
        fetch_account_tool = self._create_fetch_account_tool()
        crm_tools = self.get_langchain_tools()
        return create_react_agent(
            model="openai:gpt-4.1",
            tools=[fetch_account_tool, *crm_tools],
            prompt=prompt,
            name="crm_agent",
        )

    def _create_sales_document_agent(self) -> Runnable:
        prompt = (
            "You are a specialized sales document assistant.\n"
            "Your tasks involve creating closing plans and meeting briefs.\n"
            "After you're done, respond to the supervisor directly.\n"
            "Respond ONLY with the results of your work."
        )
        return create_react_agent(
            model="openai:gpt-4.1",
            tools=[self.create_closing_plan, self.create_meeting_brief],
            prompt=prompt,
            name="sales_document_agent",
        )

    def _create_supervisor_agent(
        self, crm_agent_name: str, sales_document_agent_name: str
    ) -> Runnable:
        handoff_to_crm = self.create_handoff_tool(
            crm_agent_name, "Assign a CRM or account-related task."
        )
        handoff_to_docs = self.create_handoff_tool(
            sales_document_agent_name, "Assign a sales document creation task."
        )

        prompt = (
            "You are a supervisor managing two agents:\n"
            f"- a '{crm_agent_name}'. Assign CRM and account tasks to this agent.\n"
            f"- a '{sales_document_agent_name}'. Assign document creation tasks to this agent.\n"
            "Assign work to one agent at a time. Do not do any work yourself."
        )

        return create_react_agent(
            model="openai:gpt-4.1",
            tools=[handoff_to_crm, handoff_to_docs],
            prompt=prompt,
            name="supervisor",
        )

    def _create_fetch_account_tool(self) -> StructuredTool:
        partial_fetch_account = functools.partial(
            self._fetch_account_logic, user_integrations=self.user_integrations
        )
        return StructuredTool.from_function(
            func=partial_fetch_account,
            name="fetch_account",
            description="Fetches account details for the current user.",
        )

    @staticmethod
    def _fetch_account_logic(user_integrations: UserIntegrations) -> dict:
        """The core logic for fetching account data."""
        print("--- Fetching account details via tool ---")
        return {
            "account_id": "acc_12345",
            "name": "Global Tech Inc.",
            "opportunity_id": "opp_67890",
            "value": 50000,
        }

    def get_langchain_tools(self) -> list[StructuredTool]:
        raw_tools = get_tools(self.user_id, self.user_integrations)
        langchain_tools = [
            StructuredTool(
                name=td.name,
                description=td.description,
                func=None,
                coroutine=td.coroutine,
                args_schema=td.args_schema,
            )
            for td in raw_tools
        ]
        return langchain_tools

    @staticmethod
    def create_handoff_tool(agent_name: str, description: str) -> tool:
        @tool(f"transfer_to_{agent_name}", description=description)
        def handoff(
            state: Annotated[ConversationState, InjectedState],
        ) -> Command:
            return Command(
                goto=agent_name,
                update=state,
                graph=Command.PARENT,
            )

        return handoff

    @tool
    def create_closing_plan(opportunity_details: str) -> str:
        """
        Generates a detailed closing plan document based on the provided
        opportunity details.
        """
        print(f"--- Creating Closing Plan for: {opportunity_details} ---")
        # In a real implementation, this would call a service or use a template
        # to generate a rich document.
        return f"Closing plan document generated for opportunity: {opportunity_details}"

    @tool
    def create_meeting_brief(meeting_subject: str, participants: list[str]) -> str:
        """
        Generates a meeting brief document for a sales meeting.
        """
        print(
            f"--- Creating Meeting Brief for: {meeting_subject} with {participants} ---"
        )
        # In a real implementation, this would fetch data about participants
        # and the meeting's context to create a comprehensive brief.
        return f"Meeting brief generated for '{meeting_subject}'."
