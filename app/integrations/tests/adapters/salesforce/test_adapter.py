import pytest

from app.integrations.adapters.salesforce.adapter import SalesforceAdapter
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_credentials(mocker):
    mock_creds = mocker.Mock(spec=ICredentials)
    mock_creds.secrets = {
        "username": "test_username",
        "password": "test_password",
        "security_token": "test_token",
    }
    return mock_creds


@pytest.fixture(autouse=True)
def mock_salesforce_handler(mocker):
    mock_handler = mocker.MagicMock()
    mock_handler.get_opportunity.return_value = {
        "Id": "001",
        "Name": "Test Opportunity",
    }
    mock_handler.update_opportunity.return_value = {
        "Id": "001",
        "Name": "Updated Opportunity",
    }
    mock_handler.list_opportunities_by_account.return_value = [
        {"Id": "001", "Name": "Test Opportunity"}
    ]
    mock_handler.search_opportunities.return_value = [
        {"Id": "001", "Name": "Found Opportunity"}
    ]
    mock_handler.get_account.return_value = {"Id": "002", "Name": "Test Account"}
    mock_handler.update_account.return_value = {
        "Id": "002",
        "Name": "Updated Account",
    }
    mock_handler.search_accounts.return_value = [{"Id": "002", "Name": "Found Account"}]
    mock_handler.get_contact.return_value = {"Id": "003", "Name": "Test Contact"}
    mock_handler.create_contact.return_value = {"Id": "003", "Name": "New Contact"}
    mock_handler.update_contact.return_value = {"Id": "003", "Name": "Updated Contact"}
    mock_handler.list_contacts_by_account.return_value = [
        {"Id": "003", "Name": "Test Contact"}
    ]
    mock_handler.search_contacts.return_value = [{"Id": "003", "Name": "Found Contact"}]
    mock_handler.get_task.return_value = {"Id": "00T", "Subject": "Test Task"}
    mock_handler.create_task.return_value = {"Id": "00T", "Subject": "New Task"}
    mock_handler.update_task.return_value = {"Id": "00T", "Subject": "Updated Task"}
    mock_handler.list_tasks_by_contact.return_value = [
        {"Id": "00T", "Subject": "Contact Task"}
    ]
    mock_handler.list_tasks_by_account.return_value = [
        {"Id": "00T", "Subject": "Account Task"}
    ]
    mock_handler.get_event.return_value = {"Id": "00U", "Subject": "Test Event"}
    mock_handler.create_event.return_value = {"Id": "00U", "Subject": "New Event"}
    mock_handler.update_event.return_value = {"Id": "00U", "Subject": "Updated Event"}
    mock_handler.list_events_by_contact.return_value = [
        {"Id": "00U", "Subject": "Contact Event"}
    ]
    mock_handler.list_events_by_account.return_value = [
        {"Id": "00U", "Subject": "Account Event"}
    ]

    # Create proper CRMAccountAccessData objects for resolve_account_access
    from app.integrations.schemas import CRMAccountAccessData

    mock_handler.resolve_account_access.return_value = [
        CRMAccountAccessData(
            account_id="002",
            account_name="Test Account",
            access_type="owner",
            access_role=None,
        )
    ]

    handler_class = mocker.patch(
        "app.integrations.adapters.salesforce.adapter.SalesforceHandler"
    )
    handler_class.return_value = mock_handler
    return mock_handler


@pytest.fixture
def salesforce_adapter(mock_credentials):
    return SalesforceAdapter(credentials=mock_credentials)


def test_init(mocker, mock_credentials):
    handler_class = mocker.patch(
        "app.integrations.adapters.salesforce.adapter.SalesforceHandler"
    )
    adapter = SalesforceAdapter(credentials=mock_credentials)
    assert adapter.credentials == mock_credentials
    handler_class.assert_called_once_with(credentials=mock_credentials)


def test_source(salesforce_adapter):
    assert salesforce_adapter.source == IntegrationSource.SALESFORCE


def test_get_opportunity(salesforce_adapter, mock_salesforce_handler):
    opportunity_id = "opp123"
    result = salesforce_adapter.get_opportunity(opportunity_id)
    mock_salesforce_handler.get_opportunity.assert_called_once_with(opportunity_id)
    assert result == {"Id": "001", "Name": "Test Opportunity"}


def test_update_opportunity(salesforce_adapter, mock_salesforce_handler):
    opportunity_id = "opp123"
    fields = {"Name": "New Name"}
    result = salesforce_adapter.update_opportunity(opportunity_id, fields)
    mock_salesforce_handler.update_opportunity.assert_called_once_with(
        opportunity_id, fields
    )
    assert result == {"Id": "001", "Name": "Updated Opportunity"}


def test_list_opportunities_by_account(salesforce_adapter, mock_salesforce_handler):
    account_id = "account456"
    limit = 50
    offset = 10
    result = salesforce_adapter.list_opportunities_by_account(
        account_id=account_id,
        limit=limit,
        offset=offset,
    )
    mock_salesforce_handler.list_opportunities_by_account.assert_called_once_with(
        account_id=account_id,
        limit=limit,
        offset=offset,
    )
    assert len(result) == 1
    assert result[0]["Id"] == "001"
    assert result[0]["Name"] == "Test Opportunity"


def test_search_opportunities(salesforce_adapter, mock_salesforce_handler):
    search_criteria = {"Name": "Big Deal"}
    limit = 50
    offset = 10
    result = salesforce_adapter.search_opportunities(
        search_criteria=search_criteria, limit=limit, offset=offset
    )
    mock_salesforce_handler.search_opportunities.assert_called_once_with(
        search_criteria=search_criteria, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "001"
    assert result[0]["Name"] == "Found Opportunity"


def test_get_account(salesforce_adapter, mock_salesforce_handler):
    account_id = "acc123"
    result = salesforce_adapter.get_account(account_id)
    mock_salesforce_handler.get_account.assert_called_once_with(account_id)
    assert result == {"Id": "002", "Name": "Test Account"}


def test_update_account(salesforce_adapter, mock_salesforce_handler):
    account_id = "acc123"
    fields = {"Name": "New Name"}
    result = salesforce_adapter.update_account(account_id, fields)
    mock_salesforce_handler.update_account.assert_called_once_with(account_id, fields)
    assert result == {"Id": "002", "Name": "Updated Account"}


def test_search_accounts(salesforce_adapter, mock_salesforce_handler):
    search_criteria = {"Name": "Tech Corp"}
    limit = 50
    offset = 10
    result = salesforce_adapter.search_accounts(
        search_criteria=search_criteria, limit=limit, offset=offset
    )
    mock_salesforce_handler.search_accounts.assert_called_once_with(
        search_criteria=search_criteria, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "002"
    assert result[0]["Name"] == "Found Account"


def test_resolve_account_access(salesforce_adapter, mock_salesforce_handler):
    user_id = "user123"
    result = salesforce_adapter.resolve_account_access(user_id)
    mock_salesforce_handler.resolve_account_access.assert_called_once_with(
        salesforce_user_id=user_id
    )
    assert len(result) == 1
    assert result[0].account_id == "002"
    assert result[0].account_name == "Test Account"
    assert result[0].access_type == "owner"
    assert result[0].access_role is None


def test_get_contact(salesforce_adapter, mock_salesforce_handler):
    contact_id = "con123"
    result = salesforce_adapter.get_contact(contact_id)
    mock_salesforce_handler.get_contact.assert_called_once_with(contact_id)
    assert result == {"Id": "003", "Name": "Test Contact"}


def test_create_contact(salesforce_adapter, mock_salesforce_handler):
    contact_data = {"FirstName": "John", "LastName": "Doe"}
    result = salesforce_adapter.create_contact(contact_data)
    mock_salesforce_handler.create_contact.assert_called_once_with(contact_data)
    assert result == {"Id": "003", "Name": "New Contact"}


def test_update_contact(salesforce_adapter, mock_salesforce_handler):
    contact_id = "con123"
    contact_data = {"FirstName": "Jane"}
    result = salesforce_adapter.update_contact(contact_id, contact_data)
    mock_salesforce_handler.update_contact.assert_called_once_with(
        contact_id, contact_data
    )
    assert result == {"Id": "003", "Name": "Updated Contact"}


def test_list_contacts_by_account(salesforce_adapter, mock_salesforce_handler):
    account_id = "acc123"
    limit = 50
    offset = 10
    result = salesforce_adapter.list_contacts_by_account(
        account_id=account_id, limit=limit, offset=offset
    )
    mock_salesforce_handler.list_contacts_by_account.assert_called_once_with(
        account_id=account_id, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "003"
    assert result[0]["Name"] == "Test Contact"


def test_search_contacts(salesforce_adapter, mock_salesforce_handler):
    search_criteria = {"Email": "<EMAIL>"}
    limit = 50
    offset = 10
    result = salesforce_adapter.search_contacts(
        search_criteria=search_criteria, limit=limit, offset=offset
    )
    mock_salesforce_handler.search_contacts.assert_called_once_with(
        search_criteria=search_criteria, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "003"
    assert result[0]["Name"] == "Found Contact"


def test_get_task(salesforce_adapter, mock_salesforce_handler):
    task_id = "tsk123"
    result = salesforce_adapter.get_task(task_id)
    mock_salesforce_handler.get_task.assert_called_once_with(task_id)
    assert result == {"Id": "00T", "Subject": "Test Task"}


def test_create_task(salesforce_adapter, mock_salesforce_handler):
    task_data = {"Subject": "New Task", "Status": "Not Started"}
    result = salesforce_adapter.create_task(task_data)
    mock_salesforce_handler.create_task.assert_called_once_with(task_data)
    assert result == {"Id": "00T", "Subject": "New Task"}


def test_update_task(salesforce_adapter, mock_salesforce_handler):
    task_id = "tsk123"
    task_data = {"Subject": "Updated Task"}
    result = salesforce_adapter.update_task(task_id, task_data)
    mock_salesforce_handler.update_task.assert_called_once_with(task_id, task_data)
    assert result == {"Id": "00T", "Subject": "Updated Task"}


def test_list_tasks_by_contact(salesforce_adapter, mock_salesforce_handler):
    contact_id = "con123"
    limit = 50
    offset = 10
    result = salesforce_adapter.list_tasks_by_contact(
        contact_id=contact_id, limit=limit, offset=offset
    )
    mock_salesforce_handler.list_tasks_by_contact.assert_called_once_with(
        contact_id=contact_id, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "00T"
    assert result[0]["Subject"] == "Contact Task"


def test_list_tasks_by_account(salesforce_adapter, mock_salesforce_handler):
    account_id = "acc123"
    limit = 50
    offset = 10
    result = salesforce_adapter.list_tasks_by_account(
        account_id=account_id, limit=limit, offset=offset
    )
    mock_salesforce_handler.list_tasks_by_account.assert_called_once_with(
        account_id=account_id, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "00T"
    assert result[0]["Subject"] == "Account Task"


def test_get_event(salesforce_adapter, mock_salesforce_handler):
    event_id = "evt123"
    result = salesforce_adapter.get_event(event_id)
    mock_salesforce_handler.get_event.assert_called_once_with(event_id)
    assert result == {"Id": "00U", "Subject": "Test Event"}


def test_create_event(salesforce_adapter, mock_salesforce_handler):
    event_data = {"Subject": "New Event", "StartDateTime": "2024-01-15T10:00:00Z"}
    result = salesforce_adapter.create_event(event_data)
    mock_salesforce_handler.create_event.assert_called_once_with(event_data)
    assert result == {"Id": "00U", "Subject": "New Event"}


def test_update_event(salesforce_adapter, mock_salesforce_handler):
    event_id = "evt123"
    event_data = {"Subject": "Updated Event"}
    result = salesforce_adapter.update_event(event_id, event_data)
    mock_salesforce_handler.update_event.assert_called_once_with(event_id, event_data)
    assert result == {"Id": "00U", "Subject": "Updated Event"}


def test_list_events_by_contact(salesforce_adapter, mock_salesforce_handler):
    contact_id = "con123"
    limit = 50
    offset = 10
    result = salesforce_adapter.list_events_by_contact(
        contact_id=contact_id, limit=limit, offset=offset
    )
    mock_salesforce_handler.list_events_by_contact.assert_called_once_with(
        contact_id=contact_id, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "00U"
    assert result[0]["Subject"] == "Contact Event"


def test_list_events_by_account(salesforce_adapter, mock_salesforce_handler):
    account_id = "acc123"
    limit = 50
    offset = 10
    result = salesforce_adapter.list_events_by_account(
        account_id=account_id, limit=limit, offset=offset
    )
    mock_salesforce_handler.list_events_by_account.assert_called_once_with(
        account_id=account_id, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "00U"
    assert result[0]["Subject"] == "Account Event"
