from app.core.database import <PERSON><PERSON>oc<PERSON>
from app.integrations.adapters.salesforce.adapter import SalesforceAdapter
from app.integrations.adapters.slack.adapter import SlackA<PERSON>pter
from app.integrations.backends.crm.backend import CRMBackend
from app.integrations.backends.messaging.backend import MessagingBackend
from app.integrations.base.adapter import Base<PERSON><PERSON>pter
from app.integrations.base.backend import BaseBackend
from app.integrations.base.crm_backend import BaseCRMBackend
from app.integrations.base.messaging_backend import BaseMessagingBackend
from app.integrations.context import IntegrationContext, IntegrationContextFactory
from app.integrations.handles import CRMHandle, MessagingHandle
from app.integrations.protocols import CRMResource, MessagingResource
from app.integrations.types import BackendType, IntegrationSource


class IntegrationFactory:
    def __init__(self, context: IntegrationContext):
        self.context = context

    def crm(self, source: IntegrationSource) -> CRMResource:
        backend = self._create_backend(source, BackendType.CRM)

        if not isinstance(backend, BaseCRMBackend):
            raise TypeError("Created backend does not implement BaseCRMBackend")

        return CRMHandle(backend)

    def messaging(self, source: IntegrationSource) -> MessagingResource:
        backend = self._create_backend(source, BackendType.MESSAGING)

        if not isinstance(backend, BaseMessagingBackend):
            raise TypeError("Created backend does not implement BaseMessagingBackend")

        return MessagingHandle(backend)

    def _create_backend(
        self,
        source: IntegrationSource,
        type_: BackendType,
    ) -> BaseBackend:
        mapping = self._get_integration_mapping()

        if source not in mapping:
            raise ValueError(f"Unsupported source: {source}")

        if type_ not in mapping[source]:
            raise ValueError(f"Unsupported backend type {type_} for source {source}")

        adapter_class, backend_class = mapping[source][type_]

        return backend_class(
            context=self.context, adapter_class=adapter_class, source=source
        )

    @staticmethod
    def _get_integration_mapping() -> dict[
        IntegrationSource,
        dict[BackendType, tuple[type[BaseAdapter], type[BaseBackend]]],
    ]:
        return {
            IntegrationSource.SALESFORCE: {
                BackendType.CRM: (SalesforceAdapter, CRMBackend),
            },
            IntegrationSource.SLACK: {
                BackendType.MESSAGING: (SlackAdapter, MessagingBackend),
            },
        }


def create_factory(
    tenant_id, db_factory=None, credentials_resolver=None
) -> IntegrationFactory:
    if db_factory is None:
        db_factory = SessionLocal

    context = IntegrationContextFactory.create_context(
        tenant_id=tenant_id,
        db_session_factory=db_factory,
        credentials_resolver=credentials_resolver,
    )

    return IntegrationFactory(context)
