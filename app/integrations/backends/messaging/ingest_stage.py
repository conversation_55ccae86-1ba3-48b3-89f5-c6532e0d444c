import datetime
import uuid

from sqlalchemy.orm import Session

from app.common.helpers.logger import get_logger
from app.common.pipeline.base_stage import BaseStage
from app.integrations.backends.messaging.channel_ingestor import (
    MessagingChannelIngestor,
)
from app.integrations.models import (
    MessagingIngestionRun,
)  # TODO: Migrate to generic model
from app.integrations.types import IntegrationSource

logger = get_logger()


class MessagingIngestStage(BaseStage):
    """
    Pipeline stage for ingesting data from messaging channels.
    Handles scheduling and execution of ingestion runs.
    """

    def __init__(
        self,
        tenant_id: uuid.UUID,
        source: IntegrationSource,
        db_session: Session,
        ingestor: MessagingChannelIngestor,
        channel_ids: list[str],
        lookback_days: int = 3,
        interval_seconds: int = 60,
        stage_id: str | None = None,
    ):
        """
        Initialize the messaging ingest stage.

        Args:
            tenant_id: The tenant identifier
            source: Integration source type
            db_session: SQLAlchemy session for tracking runs
            ingestor: The messaging channel ingestor to use
            channel_ids: List of channel IDs to ingest
            lookback_days: Number of days to look back for messages
            interval_seconds: Interval between execution cycles in seconds
            stage_id: Unique identifier for this stage
        """
        super().__init__(
            stage_id=stage_id,
            interval_seconds=interval_seconds,
            logger=logger,
        )
        self.ingestor = ingestor
        self.source = source  # Stockage du paramètre source
        self.channel_ids = channel_ids
        self.lookback_days = lookback_days
        self.tenant_id = tenant_id
        self.db_session = db_session
        self.metrics = {
            "total_runs": 0,
            "successful_runs": 0,
            "channels_processed": 0,
            "messages_ingested": 0,
            "errors_count": 0,
        }

    def execute_once(self) -> dict:
        """
        Execute one cycle of messaging channel ingestion for all configured channels.

        Returns:
            Dictionary with execution results and metrics
        """
        self.metrics["total_runs"] += 1
        results: dict = {
            "status": "success",
            "channels_processed": 0,
            "channels": {},
            "messages_ingested": 0,
        }

        # Calculate time window
        end_time = datetime.datetime.now(datetime.UTC)
        start_time = end_time - datetime.timedelta(days=self.lookback_days)

        try:
            # Process each channel
            for channel_id in self.channel_ids:
                # Create ingestion run record
                run = MessagingIngestionRun(
                    tenant_id=self.tenant_id,
                    source=self.source,
                    channel_id=channel_id,
                    slice_from_time=start_time,
                    slice_to_time=end_time,
                    status=MessagingIngestionRun.Status.IN_PROGRESS,
                    run_start=datetime.datetime.now(datetime.UTC),
                )
                self.db_session.add(run)
                self.db_session.commit()

                try:
                    self.logger.info(
                        f"Ingesting {self.source.value} channel {channel_id} "  # Utilisation de self.source
                        f"from {start_time} to {end_time}"
                    )

                    # Use the ingestor to handle this channel
                    channel_result = self.ingestor.ingest_channel(
                        channel_id, start_time, end_time
                    )

                    run.status = MessagingIngestionRun.Status.SUCCESS
                    run.messages_processed = channel_result.messages_count
                    run.inserts = channel_result.inserts
                    run.updates = channel_result.updates
                    run.deletes = channel_result.deletes
                    run.run_end = datetime.datetime.now(datetime.UTC)
                    self.db_session.commit()

                    # Update statistics
                    results["channels_processed"] += 1
                    results["messages_ingested"] += channel_result.messages_count
                    self.metrics["channels_processed"] += 1
                    self.metrics["messages_ingested"] += channel_result.messages_count

                    # Add channel-specific results
                    results["channels"][channel_id] = {
                        "status": "success",
                        "messages_count": channel_result.messages_count,
                        "inserts": channel_result.inserts,
                        "updates": channel_result.updates,
                        "deletes": channel_result.deletes,
                    }

                    self.logger.info(
                        f"Successfully ingested {self.source.value} channel {channel_id}"  # Utilisation de self.source
                    )

                except Exception as e:
                    run.status = MessagingIngestionRun.Status.FAILED
                    run.error_message = str(e)
                    run.run_end = datetime.datetime.now(datetime.UTC)
                    self.db_session.commit()

                    error_msg = f"Error ingesting {self.source.value} channel {channel_id}"  # Utilisation de self.source
                    self.logger.exception(error_msg)
                    results["channels"][channel_id] = {
                        "status": "error",
                        "error": str(e),
                    }
                    self.metrics["errors_count"] += 1

            # If any channel failed, mark overall status as partial
            if any(ch.get("status") == "error" for ch in results["channels"].values()):
                results["status"] = "partial"
            else:
                self.metrics["successful_runs"] += 1

            return results

        except Exception as e:
            self.logger.exception(f"Messaging ingestion stage failed: {self.stage_id}")
            results["status"] = "error"
            results["error"] = str(e)
            return results

    def get_status(self) -> dict:
        """Get detailed status information including metrics."""
        status = super().get_status()
        status.update(
            {
                "tenant_id": str(self.tenant_id),
                "source": str(self.source),  # Utilisation de self.source
                "channels_count": len(self.channel_ids),
                "interval_seconds": self.interval_seconds,
                "lookback_days": self.lookback_days,
                "metrics": self.metrics,
            }
        )
        return status
