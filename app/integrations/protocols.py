from collections.abc import Callable
from typing import Any, Protocol

from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.schemas import DocumentData
from app.integrations.types import IntegrationSource


class BaseIntegrationResource(Protocol):
    """Base protocol for all integration resources."""

    @property
    def source(self) -> IntegrationSource:
        """Gets the source integration."""
        ...


class CRMResource(BaseIntegrationResource, Protocol):
    # Core CRM operations
    def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        """Gets an opportunity by ID."""
        ...

    def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        """Updates an opportunity."""
        ...

    def list_opportunities_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists opportunities for an account."""
        ...

    def search_opportunities(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        """Searches opportunities by criteria."""
        ...

    def get_account(self, account_id: str) -> dict[str, Any]:
        """Gets an account by ID."""
        ...

    def update_account(self, account_id: str, fields: dict[str, Any]) -> dict[str, Any]:
        """Updates an account."""
        ...

    def search_accounts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        """Searches accounts by criteria."""
        ...

    def get_contact(self, contact_id: str) -> dict[str, Any]:
        """Gets a contact by ID."""
        ...

    def create_contact(self, contact_data: dict[str, Any]) -> dict[str, Any]:
        """Creates a new contact."""
        ...

    def update_contact(
        self, contact_id: str, contact_data: dict[str, Any]
    ) -> dict[str, Any]:
        """Updates a contact."""
        ...

    def list_contacts_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists contacts for an account."""
        ...

    def search_contacts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        """Searches contacts by criteria."""
        ...

    def get_task(self, task_id: str) -> dict[str, Any]:
        """Gets a task by ID."""
        ...

    def create_task(self, task_data: dict[str, Any]) -> dict[str, Any]:
        """Creates a new task."""
        ...

    def update_task(self, task_id: str, task_data: dict[str, Any]) -> dict[str, Any]:
        """Updates a task."""
        ...

    def list_tasks_by_contact(
        self, contact_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists tasks for a contact."""
        ...

    def list_tasks_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists tasks for an account."""
        ...

    def get_event(self, event_id: str) -> dict[str, Any]:
        """Gets an event by ID."""
        ...

    def create_event(self, event_data: dict[str, Any]) -> dict[str, Any]:
        """Creates a new event."""
        ...

    def update_event(self, event_id: str, event_data: dict[str, Any]) -> dict[str, Any]:
        """Updates an event."""
        ...

    def list_events_by_contact(
        self, contact_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists events for a contact."""
        ...

    def list_events_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists events for an account."""
        ...

    def list_account_access(
        self, crm_user_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists accounts that the specified user has access to."""
        ...

    # Sync operations
    def bulk_sync_account_access(
        self,
        crm_user_ids: list[str],
        get_credentials_resolver: Callable[[str], ICredentialsResolver] | None = None,
        interval_seconds: int = 300,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        """Starts synchronization of account access for the specified users."""
        ...


class MessagingResource(BaseIntegrationResource, Protocol):
    def search_channel_messages(
        self, channel_id: str, query: str, limit: int = 10
    ) -> list[tuple[DocumentData, float]]:
        """Searches for messages in the specified channel."""
        ...

    def start_channel_ingestion(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        lookback_days: int = 7,
        batch_size: int = 100,
        daemon_mode: bool = False,
        sequential_execution: bool = False,
    ) -> dict[str, Any]:
        """Starts ingestion of messages from the specified channels."""
        ...

    def start_channel_processing(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        batch_size: int = 100,
        daemon_mode: bool = False,
        sequential_execution: bool = False,
    ) -> dict[str, Any]:
        """Starts processing of messages from the specified channels."""
        ...
